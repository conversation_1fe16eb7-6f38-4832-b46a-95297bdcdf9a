import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate' // 数据持久化

const store = createPinia()
store.use(
  createPersistedState({
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
  }),
)

export default store

// 模块统一导出
export * from './user'
export * from './menu' // 导出菜单store
export * from './dict' // 导出字典store
export * from './recentMenu' // 导出最近访问菜单store
export * from './attend-lecture' // 导出听课记录store
export * from './competition' // 导出竞赛store
export * from './mediationCourse' // 导出调课store
export * from './schoolChange' // 导出学籍异动store
