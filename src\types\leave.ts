/**
 * 请假类型选项接口
 */
export interface LeaveTypeOption {
  /** 请假类型值 */
  value: number
  /** 请假类型名称 */
  label: string
  /** 请假类型颜色 */
  color: string
}

/**
 * 请假记录接口（旧版）
 * @deprecated 请使用新版LeaveRecordItem接口
 */
export interface LeaveRecord {
  /** 请假记录ID */
  id: number
  /** 学生学号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 请假开始时间 */
  leaveStartTime: string
  /** 请假结束时间 */
  leaveEndTime: string
  /** 请假小时数 */
  leaveHours: number
  /** 请假类型 */
  leaveType: number
  /** 请假类型名称 */
  leaveTypeName: string
  /** 请假原因 */
  leaveReason: string
  /** 附件列表 */
  attachmentList: string
  /** 晚归申请 */
  lateNightRequest: number
  /** 晚归审批状态 */
  lateNightApprovalStatus: number
  /** 第一级审批 */
  approval1: number
  /** 第二级审批 */
  approval2: number
  /** 申请时间 */
  requestTime: string
  /** 是否申请出校 */
  isApplyForExitCampus: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 请假类型颜色 */
  leaveTypeColor: string
  /** 请假时间段 */
  leaveTime: string
}

/**
 * 新版请假记录项接口
 */
export interface LeaveRecordItem {
  /** 请假记录ID */
  id: number
  /** 请假人学号 */
  qjr: string
  /** 请假人姓名 */
  qjrxm: string
  /** 请假开始时间 */
  qjkssj: string
  /** 请假结束时间 */
  qjjssj: string
  /** 请假小时数 */
  qjss: number
  /** 请假天数 */
  qjts: number
  /** 请假类型 */
  qjlx: string
  /** 请假类型名称 */
  qjlxmc: string
  /** 请假原因 */
  qjsy: string
  /** 附件列表 */
  fjlb: string
  /** 晚寝申请 */
  wqsq: number
  /** 晚寝审批状态 */
  wqspzt: number
  /** 第一级审批 */
  sp1: number
  /** 第二级审批 */
  sp2: number
  /** 申请时间 */
  sqsj: string
  /** 是否申请出校 */
  sfsqcxm: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 联系人 */
  lxr: string
  /** 联系人电话 */
  lxrdh: string
  /** 出行目的地 */
  cxmdd: string
  /** 本人电话 */
  brdh: string
  /** 销假说明 */
  xjsm: string | null
  /** 销假时间 */
  xjsj: string | null
  /** 附件列表2 */
  fjlb2: string | null
  /** 销假单位 */
  xjdw: string | null
  /** 销假审批 */
  xjsp: number
  /** 请假时间段 */
  leaveTime: string
  /** 工作流代码 */
  gzldm: string
}

/**
 * 请假统计数据接口
 */
export interface LeaveStatistics {
  /** 请假类型统计 */
  leaveTypeData: Array<{
    name: string
    value: number
  }>
  /** 统计数据 */
  statistics: {
    /** 本周请假次数 */
    thisWeek: number
    /** 本月请假次数 */
    thisMonth: number
    /** 本学期请假次数 */
    studyTerm: number
    /** 总共请假次数 */
    total: number
  }
  /** 周数据 */
  weekData: {
    /** 每天请假次数 */
    data: number[]
    /** 日期 */
    date: string[]
  }
  /** 条形图数据 */
  barData: number[]
}

/**
 * 请假查询参数接口
 */
export interface LeaveQueryParams {
  /** 页码 */
  page: number
  /** 每页条数 */
  pageSize: number
  /** 请假类型名称 */
  leaveTypeName: string[]
  /** 请假时间范围 */
  leaveTime: string[]
  /** 请假小时数 */
  leaveHours: string
  /** 请假原因 */
  leaveReason: string
  /** 申请时间范围 */
  requestTime: string[]
}

/**
 * 请假记录响应接口（旧版）
 * @deprecated 请使用新版LeaveRecordResponse接口
 */
export interface LeaveResponse {
  /** 请假记录列表 */
  items: LeaveRecord[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
  /** 统计数据 */
  statistics: LeaveStatistics
}

/**
 * 新版请假记录响应接口
 */
export interface LeaveRecordResponse {
  /** 请假记录列表 */
  items: LeaveRecordItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
  /** 统计数据 */
  statistics: LeaveStatistics
  /** 标志位 */
  flag: boolean
  /** 请假信息详情 */
  qjxx: LeaveRecordItem
}

/**
 * 请假申请表单接口
 */
export interface LeaveApplicationForm {
  /** 请假类别(事假、病假等)，从字典获取，API参数名为qjlx */
  leaveType?: number | string
  /** 请假类别(事假、病假等)，API实际参数名 */
  qjlx?: number | string
  /** 请假原因，API参数名为qjsy */
  leaveReason?: string
  /** 请假原因，API实际参数名 */
  qjsy?: string
  /** 是否申请出校，API参数名为sfsqcxm */
  isApplyForExitCampus?: number
  /** 是否申请出校，API实际参数名 */
  sfsqcxm?: number
  /** 请假类型(学习请假、晚寝请假、全部请假)，API参数名为wqsq */
  lateNightRequest?: number | string
  /** 请假类型(学习请假、晚寝请假、全部请假)，API实际参数名 */
  wqsq?: number | string
  /** 请假时间范围 */
  leaveTime?: string[]
  /** 文件列表 */
  fjlb?: string
  /** 请假开始时间，旧版API用 */
  leaveStartTime?: string
  /** 请假结束时间，旧版API用 */
  leaveEndTime?: string
  /** 请假类别，扩展字段 */
  leaveCategory?: number | string
  /** 学生学号 */
  studentCode?: string
  /** 学生姓名 */
  studentName?: string
  /** 附件列表，用于提交请假申请 */
  attachmentList?: string[]
}

/**
 * 请假相关字典类型常量
 */
export const LeaveDictTypes = {
  /** 请假类别字典 */
  LEAVE_CATEGORY: 'DM_XSQJLB',
  /** 请假类型字典 */
  LEAVE_TYPE: 'DM_XSQJDM',
  /** 审批状态字典 */
  APPROVAL_STATUS: 'DM_SPZT2',
} as const
